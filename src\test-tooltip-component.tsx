import React from 'react';
import { IconCard, Tooltip } from '@/shared/components/common';

const TestTooltipComponent: React.FC = () => {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">Tooltip Test</h1>
      
      {/* Test 1: IconCard with title prop */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Test 1: IconCard with title prop</h2>
        <div className="flex space-x-4">
          <IconCard 
            icon="plus" 
            variant="primary" 
            onClick={() => console.log('Plus clicked')}
            title="Add new item"
          />
          <IconCard 
            icon="search" 
            variant="default" 
            onClick={() => console.log('Search clicked')}
            title="Search"
          />
          <IconCard 
            icon="filter" 
            variant="default" 
            onClick={() => console.log('Filter clicked')}
            title="Filter"
          />
        </div>
      </div>

      {/* Test 2: Tooltip wrapper around IconCard */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Test 2: Tooltip wrapper around IconCard</h2>
        <div className="flex space-x-4">
          <Tooltip content="Add new item" position="bottom">
            <IconCard 
              icon="plus" 
              variant="primary" 
              onClick={() => console.log('Plus clicked')}
            />
          </Tooltip>
          <Tooltip content="Search" position="bottom">
            <IconCard 
              icon="search" 
              variant="default" 
              onClick={() => console.log('Search clicked')}
            />
          </Tooltip>
          <Tooltip content="Filter" position="bottom">
            <IconCard 
              icon="filter" 
              variant="default" 
              onClick={() => console.log('Filter clicked')}
            />
          </Tooltip>
        </div>
      </div>

      {/* Test 3: Different positions */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Test 3: Different positions</h2>
        <div className="flex space-x-4 justify-center">
          <IconCard 
            icon="arrow-up" 
            variant="default" 
            title="Top tooltip"
          />
          <IconCard 
            icon="arrow-right" 
            variant="default" 
            title="Right tooltip"
          />
          <IconCard 
            icon="arrow-down" 
            variant="default" 
            title="Bottom tooltip"
          />
          <IconCard 
            icon="arrow-left" 
            variant="default" 
            title="Left tooltip"
          />
        </div>
      </div>

      {/* Test 4: Regular buttons with tooltip */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Test 4: Regular buttons with tooltip</h2>
        <div className="flex space-x-4">
          <Tooltip content="This is a tooltip on top" position="top">
            <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
              Top
            </button>
          </Tooltip>
          <Tooltip content="This is a tooltip on right" position="right">
            <button className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
              Right
            </button>
          </Tooltip>
          <Tooltip content="This is a tooltip on bottom" position="bottom">
            <button className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
              Bottom
            </button>
          </Tooltip>
          <Tooltip content="This is a tooltip on left" position="left">
            <button className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
              Left
            </button>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default TestTooltipComponent;
