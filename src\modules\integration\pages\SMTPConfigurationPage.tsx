import React, { useState, useRef, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { Card, Button, Input, Textarea, FormItem, Form } from '@/shared/components/common';
import Toggle from '@/shared/components/common/Toggle';
import { emailServerConfigurationSchema } from '../email/schemas';
import { EmailServerConfiguration } from '../email/types';

// Định nghĩa kiểu dữ liệu cho form
export type SMTPFormValues = z.infer<typeof emailServerConfigurationSchema>;

interface SMTPConfigurationPageProps {
  /**
   * Dữ liệu ban đầu cho form (khi chỉnh sửa)
   */
  initialData?: EmailServerConfiguration | null;

  /**
   * Hàm xử lý khi submit form
   */
  onSubmit?: (values: Record<string, unknown>) => void;

  /**
   * Chế độ chỉ đọc
   */
  readOnly?: boolean;
}

/**
 * Trang cấu hình SMTP
 */
const SMTPConfigurationPage: React.FC<SMTPConfigurationPageProps> = ({
  initialData,
  onSubmit,
  readOnly = false,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const formRef = useRef(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditMode = !!initialData;

  // Memoize defaultValues để tránh re-render không cần thiết
  const defaultValues = useMemo(() => {
    return initialData
      ? {
          serverName: initialData.serverName,
          host: initialData.host,
          port: initialData.port,
          username: initialData.username,
          password: '', // Không hiển thị password cũ
          useSsl: initialData.useSsl,
          useStartTls: initialData.useStartTls,
          additionalSettings: initialData.additionalSettings
            ? JSON.stringify(initialData.additionalSettings, null, 2)
            : '',
          isActive: initialData.isActive,
        }
      : {
          serverName: '',
          host: '',
          port: 587,
          username: '',
          password: '',
          useSsl: false,
          useStartTls: true,
          additionalSettings: '',
          isActive: true,
        };
  }, [initialData]);

  // Memoize handlers để tránh re-render
  const handleFormSubmit = useCallback(
    (values: Record<string, unknown>) => {
      setIsSubmitting(true);
      try {
        // Parse additionalSettings từ JSON string nếu có
        const processedValues = {
          ...values,
          additionalSettings: values['additionalSettings']
            ? JSON.parse(values['additionalSettings'] as string)
            : undefined,
        };

        if (onSubmit) {
          onSubmit(processedValues);
        } else {
          // Default behavior - log values
          console.log('SMTP Configuration:', processedValues);
          alert(t('integration:smtp.saveSuccess', 'Cấu hình SMTP đã được lưu thành công!'));
        }
      } catch (error) {
        console.error('Error saving SMTP configuration:', error);
        alert(t('integration:smtp.saveError', 'Có lỗi xảy ra khi lưu cấu hình SMTP'));
      } finally {
        setIsSubmitting(false);
      }
    },
    [onSubmit, t]
  );

  return (
    <div className="w-full bg-background text-foreground">
      <Card>
        <Form
          schema={emailServerConfigurationSchema}
          onSubmit={handleFormSubmit}
          className="space-y-6"
          defaultValues={defaultValues}
          ref={formRef}
          useDefaultValuesOnce={true}
        >
          <FormItem
            name="serverName"
            label={t('integration:smtp.form.fields.serverName', 'Tên máy chủ')}
            required
          >
            <Input
              placeholder={t(
                'integration:smtp.form.placeholders.serverName',
                'Nhập tên máy chủ SMTP'
              )}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="host"
            label={t('integration:smtp.form.fields.host', 'Máy chủ SMTP')}
            required
          >
            <Input
              placeholder={t('integration:smtp.form.placeholders.host', 'smtp.gmail.com')}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          <FormItem name="port" label={t('integration:smtp.form.fields.port', 'Cổng')} required>
            <Input
              type="number"
              placeholder={t('integration:smtp.form.placeholders.port', '587')}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="username"
            label={t('integration:smtp.form.fields.username', 'Tên đăng nhập')}
            required
          >
            <Input
              type="email"
              placeholder={t(
                'integration:smtp.form.placeholders.username',
                '<EMAIL>'
              )}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="password"
            label={t('integration:smtp.form.fields.password', 'Mật khẩu')}
            required={!isEditMode}
          >
            <Input
              type="password"
              placeholder={t(
                'integration:smtp.form.placeholders.password',
                'Nhập mật khẩu hoặc app password'
              )}
              disabled={readOnly || isSubmitting}
              fullWidth
            />
          </FormItem>

          {/* SSL/TLS Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <FormItem name="useSsl" label={t('integration:smtp.form.fields.useSsl', 'Sử dụng SSL')}>
              <Toggle disabled={readOnly || isSubmitting} label="" />
            </FormItem>

            <FormItem
              name="useStartTls"
              label={t('integration:smtp.form.fields.useStartTls', 'Sử dụng StartTLS')}
            >
              <Toggle disabled={readOnly || isSubmitting} label="" />
            </FormItem>

            <FormItem
              name="isActive"
              label={t('integration:smtp.form.fields.isActive', 'Kích hoạt')}
            >
              <Toggle disabled={readOnly || isSubmitting} label="" />
            </FormItem>
          </div>

          {/* Additional Settings */}
          <FormItem
            name="additionalSettings"
            label={t('integration:smtp.form.fields.additionalSettings', 'Cài đặt bổ sung (JSON)')}
          >
            <Textarea
              placeholder={t(
                'integration:smtp.form.placeholders.additionalSettings',
                '{"timeout": 30000}'
              )}
              disabled={readOnly || isSubmitting}
              rows={4}
              fullWidth
            />
          </FormItem>

          {/* Form Actions */}
          {!readOnly && (
            <div className="flex justify-end space-x-4 pt-4">
              <Button type="submit" variant="primary" isLoading={isSubmitting}>
                {t('integration:smtp.actions.save', 'Lưu cấu hình')}
              </Button>
            </div>
          )}
        </Form>
      </Card>
    </div>
  );
};

export default SMTPConfigurationPage;
