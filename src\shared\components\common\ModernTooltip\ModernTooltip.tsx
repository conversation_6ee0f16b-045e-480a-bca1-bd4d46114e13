import React, { useState, useRef } from 'react';
import { Z_INDEX } from '@/shared/constants/breakpoints';

export interface ModernTooltipProps {
  /**
   * Nội dung của tooltip
   */
  content: React.ReactNode;

  /**
   * Children element sẽ trigger tooltip
   */
  children: React.ReactNode;

  /**
   * Vị trí hiển thị tooltip
   */
  position?: 'top' | 'right' | 'bottom' | 'left';

  /**
   * Độ trễ hiển thị tooltip (ms)
   */
  delay?: number;

  /**
   * Class bổ sung cho tooltip
   */
  className?: string;

  /**
   * Màu nền của tooltip
   */
  variant?: 'dark' | 'light';

  /**
   * Kích thước của tooltip
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Có hiển thị mũi tên không
   */
  arrow?: boolean;
}

/**
 * Component ModernTooltip hiển thị tooltip với thiết kế hiện đại
 */
const ModernTooltip: React.FC<ModernTooltipProps> = ({
  content,
  children,
  position = 'top',
  delay = 300,
  className = '',
  variant = 'dark',
  size = 'md',
  arrow = true,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [coords, setCoords] = useState({ x: 0, y: 0 });
  const tooltipRef = useRef<HTMLDivElement>(null);
  const targetRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Xác định kích thước dựa trên prop size
  const sizeClasses = {
    sm: 'text-xs py-1 px-2',
    md: 'text-sm py-1.5 px-3',
    lg: 'text-base py-2 px-4',
  }[size];

  // Xác định màu sắc dựa trên prop variant
  const variantClasses = {
    dark: 'bg-gray-800 text-white dark:bg-gray-900',
    light:
      'bg-white text-gray-800 dark:bg-dark-light dark:text-gray-200 border border-gray-200 dark:border-gray-700',
  }[variant];

  // Xác định class cho mũi tên dựa trên position và variant
  const arrowClasses = {
    dark: {
      top: 'border-t-gray-800 dark:border-t-gray-900',
      right: 'border-r-gray-800 dark:border-r-gray-900',
      bottom: 'border-b-gray-800 dark:border-b-gray-900',
      left: 'border-l-gray-800 dark:border-l-gray-900',
    },
    light: {
      top: 'border-t-white dark:border-t-dark-light',
      right: 'border-r-white dark:border-r-dark-light',
      bottom: 'border-b-white dark:border-b-dark-light',
      left: 'border-l-white dark:border-l-dark-light',
    },
  }[variant][position];

  // Xử lý hiển thị tooltip
  const handleMouseEnter = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // Đảm bảo tooltip không hiển thị ngay lập tức
    setIsVisible(false);

    timerRef.current = setTimeout(() => {
      if (targetRef.current) {
        const rect = targetRef.current.getBoundingClientRect();

        // Tính toán vị trí tooltip dựa trên position
        let x = 0;
        let y = 0;

        switch (position) {
          case 'top':
            x = rect.left + rect.width / 2;
            y = rect.top;
            break;
          case 'right':
            x = rect.right;
            y = rect.top + rect.height / 2;
            break;
          case 'bottom':
            x = rect.left + rect.width / 2;
            y = rect.bottom;
            break;
          case 'left':
            x = rect.left;
            y = rect.top + rect.height / 2;
            break;
        }

        setCoords({ x, y });
        setIsVisible(true);
      }
    }, delay);
  };

  // Xử lý ẩn tooltip
  const handleMouseLeave = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    setIsVisible(false);
  };

  // Xác định style cho tooltip dựa trên position và coords
  const getTooltipStyle = () => {
    if (!tooltipRef.current) return {};

    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const offset = 8; // Khoảng cách giữa tooltip và target

    switch (position) {
      case 'top':
        return {
          left: `${coords.x - tooltipRect.width / 2}px`,
          top: `${coords.y - tooltipRect.height - offset}px`,
        };
      case 'right':
        return {
          left: `${coords.x + offset}px`,
          top: `${coords.y - tooltipRect.height / 2}px`,
        };
      case 'bottom':
        return {
          left: `${coords.x - tooltipRect.width / 2}px`,
          top: `${coords.y + offset}px`,
        };
      case 'left':
        return {
          left: `${coords.x - tooltipRect.width - offset}px`,
          top: `${coords.y - tooltipRect.height / 2}px`,
        };
      default:
        return {};
    }
  };

  // Xác định style cho mũi tên dựa trên position
  const getArrowStyle = () => {
    switch (position) {
      case 'top':
        return {
          bottom: '-4px',
          left: '50%',
          transform: 'translateX(-50%) rotate(45deg)',
          borderWidth: '0 0 4px 4px',
        };
      case 'right':
        return {
          left: '-4px',
          top: '50%',
          transform: 'translateY(-50%) rotate(45deg)',
          borderWidth: '0 0 4px 4px',
        };
      case 'bottom':
        return {
          top: '-4px',
          left: '50%',
          transform: 'translateX(-50%) rotate(45deg)',
          borderWidth: '4px 0 0 4px',
        };
      case 'left':
        return {
          right: '-4px',
          top: '50%',
          transform: 'translateY(-50%) rotate(45deg)',
          borderWidth: '4px 4px 0 0',
        };
      default:
        return {};
    }
  };

  return (
    <>
      <div
        ref={targetRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="inline-block"
      >
        {children}
      </div>

      {isVisible && (
        <div
          ref={tooltipRef}
          className={`
            fixed rounded-md shadow-md
            ${sizeClasses}
            ${variantClasses}
            ${className}
            animate-fade-in pointer-events-none
          `}
          style={{ zIndex: Z_INDEX.tooltip }}
          style={getTooltipStyle()}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onClick={e => e.stopPropagation()}
        >
          {content}

          {arrow && (
            <div
              className={`absolute w-2 h-2 ${arrowClasses} bg-inherit`}
              style={getArrowStyle()}
            />
          )}
        </div>
      )}
    </>
  );
};

export default ModernTooltip;
