import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Select,
  Icon,
  Typography,
  Loading,
  CollapsibleCard,
  PhoneInputWithCountry,
  DatePickerFormField,
  IconCard,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { FormStatus } from '../types/profile.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { PROFILE_CARD_IDS } from '../constants/profile-cards';
import { useCurrentUser, useUpdatePersonalInfo } from '../hooks/useUser';
import { GenderEnum, UpdatePersonalInfoDto } from '../types/user.types';
import { createPersonalInfoSchema, PersonalInfoSchema } from '../schemas';
import ProfileCard from './ProfileCard';

/**
 * Component form thông tin cá nhân
 */
const PersonalInfoForm: React.FC = () => {
  const { t } = useTranslation(['profile', 'validation']);
  const [formStatus, setFormStatus] = useState<FormStatus>(FormStatus.IDLE);
  const { showNotification } = useSmartNotification();

  // Ref để truy cập form methods
  const formRef = useRef<FormRef<PersonalInfoSchema>>(null);

  // Tạo schema với hàm t để hỗ trợ đa ngôn ngữ
  const personalInfoSchema = createPersonalInfoSchema(t);

  // Sử dụng hook để lấy thông tin người dùng - chỉ khi card được mở
  const { data: user, isLoading: isLoadingUser, error: userError } = useCurrentUser();

  // Sử dụng hook để cập nhật thông tin cá nhân
  const updatePersonalInfoMutation = useUpdatePersonalInfo();

  // Xử lý khi submit form
  const onSubmit = (data: PersonalInfoSchema) => {
    // Sử dụng dữ liệu từ form
    const submittingData: PersonalInfoSchema = data;

    // Kiểm tra dữ liệu trước khi gửi
    if (!submittingData.fullName) {
      showNotification('error', t('validation:required', 'Họ và tên là bắt buộc'));
      return;
    }

    if (!submittingData.gender) {
      showNotification('error', t('validation:required', 'Giới tính là bắt buộc'));
      return;
    }

    if (!submittingData.address) {
      showNotification('error', t('validation:required', 'Địa chỉ là bắt buộc'));
      return;
    }

    if (!submittingData.email) {
      showNotification('error', t('validation:required', 'Email là bắt buộc'));
      return;
    }

    if (!submittingData.phoneNumber) {
      showNotification('error', t('validation:required', 'Số điện thoại là bắt buộc'));
      return;
    }

    // Cập nhật trạng thái form
    setFormStatus(FormStatus.SUBMITTING);

    // Chuyển đổi dữ liệu từ form sang DTO
    const updateData: UpdatePersonalInfoDto = {
      fullName: submittingData.fullName,
      gender: submittingData.gender,
      ...(submittingData.dateOfBirth && { dateOfBirth: submittingData.dateOfBirth }),
      address: submittingData.address,
      phoneNumber: submittingData.phoneNumber,
    };

    // Gọi API để cập nhật thông tin
    updatePersonalInfoMutation.mutate(updateData, {
      onSuccess: response => {
        console.log('Update success:', response);
        setFormStatus(FormStatus.IDLE);

        showNotification(
          'success',
          t('profile:messages.updateSuccess', 'Cập nhật thông tin cá nhân thành công')
        );
      },
      onError: error => {
        console.error('Error updating profile:', error);
        setFormStatus(FormStatus.IDLE);

        // Hiển thị thông báo lỗi
        let errorMessage = t(
          'profile:messages.updateError',
          'Có lỗi xảy ra khi cập nhật thông tin cá nhân'
        );

        // Kiểm tra xem error có phải là AxiosError không
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as { response?: { data?: { message?: string } } };
          if (axiosError.response?.data?.message) {
            errorMessage = axiosError.response.data.message;
          }
        }

        showNotification('error', errorMessage);
      },
    });
  };

  // Xử lý khi hủy thay đổi (reset về giá trị ban đầu)
  const handleCancel = (e?: React.MouseEvent) => {
    // Ngăn chặn sự kiện mặc định (nếu có) để tránh gửi form
    if (e) {
      e.preventDefault();
    }

    // Reset form về giá trị ban đầu từ user data
    if (formRef.current && user) {
      formRef.current.reset({
        fullName: user.fullName || '',
        gender: user.gender || GenderEnum.MALE,
        dateOfBirth: user.dateOfBirth || '',
        address: user.address || '',
        email: user.email || '',
        phoneNumber: user.phoneNumber || '',
      });
    }
  };

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoadingUser) {
    return (
      <CollapsibleCard title={t('profile:personalInfo.title')} className="mb-6">
        <div className="flex justify-center items-center py-8">
          <Loading size="lg" />
          <span className="ml-2">{t('profile:loading.loadingProfile')}</span>
        </div>
      </CollapsibleCard>
    );
  }

  // Hiển thị lỗi nếu có
  if (userError) {
    return (
      <CollapsibleCard title={t('profile:personalInfo.title')} className="mb-6">
        <div className="text-red-500 py-4">{t('profile:error.loadingProfile')}</div>
      </CollapsibleCard>
    );
  }

  // Danh sách giới tính
  const genderOptions = [
    { value: GenderEnum.MALE, label: t('profile:personalInfo.genderOptions.male') },
    { value: GenderEnum.FEMALE, label: t('profile:personalInfo.genderOptions.female') },
    { value: GenderEnum.OTHER, label: t('profile:personalInfo.genderOptions.other') },
  ];

  const cardTitle = (
    <div className="flex items-center">
      <Icon name="user" className="mr-2 text-primary" />
      <Typography variant="subtitle1" weight="semibold" color="dark">
        {t('profile:personalInfo.title')}
      </Typography>
    </div>
  );

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoadingUser) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.PERSONAL_INFO} title={cardTitle}>
        <div className="flex justify-center items-center py-8">
          <Loading size="lg" />
          <span className="ml-2">{t('profile:loading.loadingProfile')}</span>
        </div>
      </ProfileCard>
    );
  }

  // Hiển thị lỗi nếu có
  if (userError) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.PERSONAL_INFO} title={cardTitle}>
        <div className="text-red-500 py-4">{t('profile:error.loadingProfile')}</div>
      </ProfileCard>
    );
  }

  return (
    <ProfileCard cardId={PROFILE_CARD_IDS.PERSONAL_INFO} title={cardTitle}>
      <Form
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={formRef as any}
        schema={personalInfoSchema}
        onSubmit={data => {
          // Kiểm tra xem form có đang ở trạng thái submitting không
          if (formStatus === FormStatus.SUBMITTING) {
            return;
          }
          onSubmit(data as PersonalInfoSchema);
        }}
        defaultValues={{
          fullName: user?.fullName || '',
          gender: user?.gender || GenderEnum.MALE,
          dateOfBirth: user?.dateOfBirth || '',
          address: user?.address || '',
          email: user?.email || '',
          phoneNumber: user?.phoneNumber || '',
        }}
      >
        <div className="space-y-6">
          {/* Họ và tên */}
          <FormItem name="fullName" label={t('profile:personalInfo.fullName')} required>
            <Input placeholder={t('profile:personalInfo.fullName')} fullWidth />
          </FormItem>

          {/* Giới tính */}
          <FormItem name="gender" label={t('profile:personalInfo.gender')} required>
            <Select
              options={genderOptions}
              placeholder={t('profile:personalInfo.gender')}
              fullWidth
            />
          </FormItem>

          {/* Ngày sinh */}
          <FormItem name="dateOfBirth" label={t('profile:personalInfo.birthDate')}>
            <DatePickerFormField
              placeholder={t('profile:personalInfo.birthDate')}
              fullWidth
              stringFormat="yyyy-MM-dd"
            />
          </FormItem>

          {/* Địa chỉ */}
          <FormItem name="address" label={t('profile:personalInfo.address')} required>
            <Input placeholder={t('profile:personalInfo.address')} fullWidth />
          </FormItem>

          {/* Email */}
          <FormItem
            name="email"
            label={
              <div className="flex items-center">
                <span>{t('profile:personalInfo.email')}</span>
                {user && user.isVerifyEmail && (
                  <Icon name="check" className="ml-2 text-green-500" />
                )}
              </div>
            }
            required={!user?.isVerifyEmail}
          >
            <Input
              disabled={user && user.isVerifyEmail}
              placeholder={t('profile:personalInfo.email')}
              type="email"
              fullWidth
            />
          </FormItem>

          {/* Số điện thoại */}
          <FormItem
            name="phoneNumber"
            label={
              user && user.isVerifyPhone ? (
                <div className="flex items-center">
                  <span>{t('profile:personalInfo.phone')}</span>
                  {user && user.isVerifyPhone && (
                    <Icon name="check" className="ml-2 text-green-500" />
                  )}
                </div>
              ) : (
                t('profile:personalInfo.phone')
              )
            }
            required={!user?.isVerifyPhone}
          >
            <PhoneInputWithCountry
              disabled={Boolean(user && user.isVerifyPhone)}
              placeholder={t('profile:personalInfo.phone')}
              fullWidth
              defaultCountry="VN"
            />
          </FormItem>

          {/* Buttons - Luôn hiển thị để test */}
          <div className="flex justify-end space-x-2 pt-4">
            <IconCard
              icon="x"
              variant="secondary"
              size="md"
              title={t('profile:buttons.cancel')}
              onClick={handleCancel}
              disabled={formStatus === FormStatus.SUBMITTING}
            />
            <IconCard
              icon="save"
              variant="primary"
              size="md"
              title={t('profile:buttons.save')}
              onClick={() => {
                // Trigger form submit programmatically
                formRef.current?.submit();
              }}
              disabled={formStatus === FormStatus.SUBMITTING}
            />
          </div>
        </div>
      </Form>
    </ProfileCard>
  );
};

export default PersonalInfoForm;
