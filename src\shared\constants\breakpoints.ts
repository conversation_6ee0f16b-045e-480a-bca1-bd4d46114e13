/**
 * Breakpoints for responsive design
 * Based on Tailwind CSS default breakpoints
 *
 * Usage:
 * - In CSS/Tailwind: sm:text-lg md:text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl
 * - In JS: if (window.innerWidth >= BREAKPOINTS.md) { ... }
 * - With useMediaQuery: useMediaQuery(`(min-width: ${BREAKPOINTS.md}px)`)
 */

export const BREAKPOINTS = {
  xs: 0, // Extra small devices (portrait phones)
  sm: 640, // Small devices (landscape phones)
  md: 768, // Medium devices (tablets)
  lg: 1024, // Large devices (desktops)
  xl: 1280, // Extra large devices (large desktops)
  '2xl': 1536, // 2X Extra large devices
} as const;

export type Breakpoint = keyof typeof BREAKPOINTS;

/**
 * Media query strings for each breakpoint
 * For use with useMediaQuery hook
 */
export const MEDIA_QUERIES = {
  xs: `(min-width: ${BREAKPOINTS.xs}px)`,
  sm: `(min-width: ${BREAKPOINTS.sm}px)`,
  md: `(min-width: ${BREAKPOINTS.md}px)`,
  lg: `(min-width: ${BREAKPOINTS.lg}px)`,
  xl: `(min-width: ${BREAKPOINTS.xl}px)`,
  '2xl': `(min-width: ${BREAKPOINTS['2xl']}px)`,

  // Max width queries
  xsOnly: `(max-width: ${BREAKPOINTS.sm - 1}px)`,
  smOnly: `(min-width: ${BREAKPOINTS.sm}px) and (max-width: ${BREAKPOINTS.md - 1}px)`,
  mdOnly: `(min-width: ${BREAKPOINTS.md}px) and (max-width: ${BREAKPOINTS.lg - 1}px)`,
  lgOnly: `(min-width: ${BREAKPOINTS.lg}px) and (max-width: ${BREAKPOINTS.xl - 1}px)`,
  xlOnly: `(min-width: ${BREAKPOINTS.xl}px) and (max-width: ${BREAKPOINTS['2xl'] - 1}px)`,
  '2xlOnly': `(min-width: ${BREAKPOINTS['2xl']}px)`,

  // Device specific
  mobile: `(max-width: ${BREAKPOINTS.md - 1}px)`,
  tablet: `(min-width: ${BREAKPOINTS.md}px) and (max-width: ${BREAKPOINTS.lg - 1}px)`,
  desktop: `(min-width: ${BREAKPOINTS.lg}px)`,

  // Orientation
  portrait: '(orientation: portrait)',
  landscape: '(orientation: landscape)',
} as const;

export type MediaQuery = keyof typeof MEDIA_QUERIES;

/**
 * Container max widths for each breakpoint
 * For use with container classes
 */
export const CONTAINER_MAX_WIDTHS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

/**
 * Grid column counts for each breakpoint
 * For use with grid layouts
 */
export const GRID_COLUMNS = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 12,
  xl: 12,
  '2xl': 12,
} as const;

/**
 * Spacing scale for each breakpoint
 * For use with margins, paddings, gaps, etc.
 */
export const SPACING_SCALE = {
  xs: {
    base: 4, // 4px base unit
    scale: 1, // Multiplier
  },
  sm: {
    base: 4,
    scale: 1,
  },
  md: {
    base: 4,
    scale: 1.25, // 25% larger spacing on tablets
  },
  lg: {
    base: 4,
    scale: 1.5, // 50% larger spacing on desktops
  },
  xl: {
    base: 4,
    scale: 1.75,
  },
  '2xl': {
    base: 4,
    scale: 2,
  },
} as const;

/**
 * Font size scale for each breakpoint
 * For use with typography
 */
export const FONT_SIZE_SCALE = {
  xs: {
    base: 16, // 16px base font size
    scale: 1, // Multiplier
  },
  sm: {
    base: 16,
    scale: 1,
  },
  md: {
    base: 16,
    scale: 1.125, // 12.5% larger fonts on tablets
  },
  lg: {
    base: 16,
    scale: 1.25, // 25% larger fonts on desktops
  },
  xl: {
    base: 16,
    scale: 1.375,
  },
  '2xl': {
    base: 16,
    scale: 1.5,
  },
} as const;

/**
 * Z-index values for consistent layering
 */
export const Z_INDEX = {
  hide: -1,
  base: 0,
  content: 10,
  header: 20,
  sticky: 40,
  fixed: 50,
  card: 100, // Card components
  popover: 9200, // Popover
  toast: 9300, // Toast notifications
  modal: 9500, // Modal dialogs
  overlay: 9800, // Overlay backgrounds
  dropdown: 99999, // Dropdown menus
  modernMenu: 100000, // Modern menu components
  tooltip: 999999, // Tooltip - cao nhất để luôn hiển thị trên cùng
} as const;

export type ZIndex = keyof typeof Z_INDEX;
