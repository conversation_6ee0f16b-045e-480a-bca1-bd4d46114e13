{"tools": {"title": "Tools", "description": "Manage your tools", "noTools": "No tools found", "noDescription": "No description", "cloneAllPublic": "Clone all public tools", "cloning": "Cloning...", "hasUpdate": "Has update", "noUpdate": "No update", "update": "Update", "activate": "Activate", "deactivate": "Deactivate", "updateVersionSuccess": "Version updated successfully!", "updateVersionError": "An error occurred while updating the version. Please try again.", "status": {"draft": "Draft", "approved": "Approved", "deprecated": "Deprecated", "pending": "Pending", "rejected": "Rejected"}, "form": {"name": "Tool name", "description": "Description", "version": "Version", "content": "Content", "schema": "<PERSON><PERSON><PERSON>", "parameters": "Parameters", "save": "Save", "cancel": "Cancel", "edit": "Edit", "create": "Create", "update": "Update", "delete": "Delete", "view": "View", "close": "Close"}, "actions": {"viewVersions": "View versions", "updateFromAdmin": "Update from Admin", "rollbackToAdmin": "Rollback to <PERSON><PERSON>", "toggleActive": "Toggle Active", "clone": "<PERSON><PERSON>", "export": "Export", "import": "Import"}, "messages": {"createSuccess": "Tool created successfully!", "createError": "An error occurred while creating the tool", "updateSuccess": "Tool updated successfully!", "updateError": "An error occurred while updating the tool", "deleteSuccess": "Tool deleted successfully!", "deleteError": "An error occurred while deleting the tool", "activateSuccess": "Tool activated successfully!", "deactivateSuccess": "Tool deactivated successfully!", "toggleError": "An error occurred while changing tool status", "cloneSuccess": "Tool cloned successfully!", "cloneError": "An error occurred while cloning the tool", "confirmDelete": "Are you sure you want to delete this tool?", "confirmDeactivate": "Are you sure you want to deactivate this tool?"}, "validation": {"nameRequired": "Tool name is required", "nameMinLength": "Tool name must be at least 3 characters", "nameMaxLength": "Tool name must not exceed 100 characters", "descriptionMaxLength": "Description must not exceed 500 characters", "versionRequired": "Version is required", "versionFormat": "Version must be in x.y.z format", "contentRequired": "Content is required", "schemaRequired": "Schema is required", "schemaInvalid": "<PERSON><PERSON><PERSON> is invalid", "parametersInvalid": "Parameters are invalid"}, "table": {"name": "Name", "description": "Description", "status": "Status", "version": "Version", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions", "active": "Active", "hasUpdate": "Has Update"}, "filters": {"all": "All", "active": "Active", "inactive": "Inactive", "hasUpdate": "Has update", "noUpdate": "No update", "search": "Search tools..."}, "versions": {"title": "Tool Versions", "current": "Current", "latest": "Latest", "create": "Create new version", "edit": "Edit version", "delete": "Delete version", "activate": "Activate version", "changelog": "Changelog", "releaseNotes": "Release notes"}, "integration": {"title": "Tool Integration", "configure": "Configure", "test": "Test", "enable": "Enable", "disable": "Disable", "settings": "Settings", "apiKey": "API Key", "webhook": "Webhook", "callback": "Callback URL", "name": "Name", "description": "Description", "baseUrl": "Base URL", "openapiSpec": "OpenAPI Specification", "authentication": "Authentication", "authType": "Authentication Type", "basicInfo": "Basic Information", "namePlaceholder": "Enter integration name", "descriptionPlaceholder": "Enter integration description", "baseUrlPlaceholder": "https://api.example.com", "openapiPlaceholder": "Paste your OpenAPI specification in JSON format", "viewIntegration": "View Integration", "editIntegration": "Edit Integration", "createIntegration": "Create Integration", "testConnection": "Test Connection", "refreshSpec": "Refresh Spec", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this integration? This action cannot be undone.", "connectionTestResult": "Connection Test Result", "apiKeyConfig": "API Key Configuration", "oauthConfig": "OAuth Configuration", "apiKeyLocation": "Location", "paramName": "Parameter Name", "paramNamePlaceholder": "e.g., X-API-Key", "schemeName": "Scheme Name", "schemeNamePlaceholder": "e.g., ApiKeyAuth", "apiKeyPlaceholder": "Enter your API key", "token": "Token", "tokenPlaceholder": "Enter your OAuth token", "tokenSource": "Token Source", "auth": {"none": "No Authentication", "apiKey": "API Key", "oauth": "OAuth"}, "table": {"name": "Name", "endpoint": "Endpoint", "method": "Method", "authType": "Auth Type", "status": "Status", "createdAt": "Created"}, "validation": {"invalidOpenApiFormat": "Invalid OpenAPI format. Must include openapi, info, and paths properties.", "invalidJson": "Invalid JSON format", "openapiRequired": "Valid OpenAPI specification is required", "invalidUrl": "Invalid URL format", "apiKeyRequired": "API Key is required", "paramNameRequired": "Parameter name is required", "tokenRequired": "Token is required"}}, "management": {"title": "Tool Management", "overview": "Overview", "statistics": "Statistics", "usage": "Usage", "performance": "Performance", "logs": "Logs", "monitoring": "Monitoring"}}}