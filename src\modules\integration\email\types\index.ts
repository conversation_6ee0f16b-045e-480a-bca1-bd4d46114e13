/**
 * Email Server Configuration Types
 */

import { SortDirection } from '@/shared/dto/request/query.dto';

// Export provider types
export * from './providers';

// Export constants
export * from '../constants';

/**
 * Type for additional email server settings
 */
export type EmailServerAdditionalSettings = Record<string, string | number | boolean>;

export interface EmailServerConfiguration {
  id?: number;
  serverName: string;
  host: string;
  port: number;
  username: string;
  password: string;
  useSsl: boolean;
  useStartTls: boolean;
  isActive: boolean;
  additionalSettings?: EmailServerAdditionalSettings;
  createdAt?: string; // Backend trả về string timestamp
  updatedAt?: string; // Backend trả về string timestamp
}

export interface CreateEmailServerDto {
  serverName: string;
  host: string;
  port: number;
  username: string;
  password: string;
  useSsl: boolean;
  useStartTls: boolean;
  isActive: boolean;
  additionalSettings?: EmailServerAdditionalSettings;
}

export interface UpdateEmailServerDto {
  serverName?: string;
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  useSsl?: boolean;
  useStartTls?: boolean;
  isActive?: boolean;
  additionalSettings?: EmailServerAdditionalSettings;
}

export interface TestEmailServerDto {
  recipientEmail?: string;
  subject?: string;
}

/**
 * DTO cho cấu hình máy chủ email trong việc test kết nối
 */
export interface EmailServerConfigDto {
  serverName: string;
  host: string;
  port: number;
  username: string;
  password: string;
  useSsl: boolean;
  useStartTls?: boolean;
  additionalSettings?: Record<string, unknown>;
}

/**
 * DTO cho việc kiểm tra kết nối máy chủ email với cấu hình trực tiếp
 */
export interface TestEmailServerWithConfigDto {
  emailServerConfig: EmailServerConfigDto;
  testInfo: TestEmailServerDto;
}

export interface EmailServerQueryParams {
  page?: number;
  limit?: number;
  search?: string | undefined;
  sortBy?: string;
  sortDirection?: SortDirection;
  isActive?: boolean;
}

/**
 * Type for email server test result details
 */
export type EmailServerTestDetails = {
  error?: string;
  statusCode?: number;
  responseTime?: number;
  serverResponse?: string;
} | Record<string, unknown>;

export interface EmailServerTestResult {
  success: boolean;
  message: string;
  details?: EmailServerTestDetails;
}

export interface EmailServerFormData extends Omit<CreateEmailServerDto, 'additionalSettings'> {
  additionalSettings?: string; // JSON string for form handling
}
