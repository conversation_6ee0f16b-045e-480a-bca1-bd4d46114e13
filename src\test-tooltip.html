<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tooltip Test</title>
    <style>
        .tooltip-container {
            position: relative;
            display: inline-block;
            margin: 50px;
        }
        
        .tooltip-trigger {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .tooltip {
            position: absolute;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            white-space: nowrap;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.2s, visibility 0.2s;
        }
        
        .tooltip.visible {
            opacity: 1;
            visibility: visible;
        }
        
        .tooltip.bottom {
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            margin-top: 8px;
        }
        
        .tooltip.top {
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="tooltip-container">
        <button class="tooltip-trigger" id="testBtn">Hover me</button>
        <div class="tooltip bottom" id="tooltip">This is a test tooltip</div>
    </div>

    <script>
        const btn = document.getElementById('testBtn');
        const tooltip = document.getElementById('tooltip');
        
        btn.addEventListener('mouseenter', () => {
            tooltip.classList.add('visible');
        });
        
        btn.addEventListener('mouseleave', () => {
            tooltip.classList.remove('visible');
        });
    </script>
</body>
</html>
