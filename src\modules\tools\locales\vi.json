{"tools": {"title": "<PERSON><PERSON><PERSON> cụ", "description": "<PERSON><PERSON><PERSON><PERSON> lý các công cụ của bạn", "noTools": "<PERSON><PERSON><PERSON><PERSON> tìm thấy công cụ nào", "noDescription": "<PERSON><PERSON><PERSON><PERSON> có mô tả", "cloneAllPublic": "<PERSON><PERSON> ch<PERSON><PERSON> tất cả công cụ công khai", "cloning": "Đang sao chép...", "hasUpdate": "<PERSON><PERSON> c<PERSON> nh<PERSON>t", "noUpdate": "<PERSON><PERSON><PERSON><PERSON> có cập nhật", "update": "<PERSON><PERSON><PERSON>", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "updateVersionSuccess": "<PERSON><PERSON><PERSON> bản đã đư<PERSON><PERSON> cập nhật thành công!", "updateVersionError": "<PERSON><PERSON> lỗi xảy ra khi cập nhật phiên bản. <PERSON><PERSON> lòng thử lại.", "status": {"draft": "<PERSON><PERSON><PERSON>", "approved": "Đ<PERSON>", "deprecated": "<PERSON><PERSON><PERSON><PERSON> dùng", "pending": "<PERSON>ờ <PERSON>", "rejected": "<PERSON><PERSON> từ chối"}, "form": {"name": "<PERSON><PERSON><PERSON> công cụ", "description": "<PERSON><PERSON>", "version": "<PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON>i dung", "schema": "<PERSON><PERSON><PERSON>", "parameters": "<PERSON>ham s<PERSON>", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "create": "<PERSON><PERSON><PERSON> mới", "update": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "view": "Xem", "close": "Đ<PERSON><PERSON>"}, "actions": {"viewVersions": "<PERSON><PERSON> b<PERSON>n", "updateFromAdmin": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> từ <PERSON>", "rollbackToAdmin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "toggleActive": "Bật/Tắt", "clone": "Sao chép", "export": "<PERSON><PERSON><PERSON>", "import": "<PERSON><PERSON><PERSON><PERSON>"}, "messages": {"createSuccess": "Tạo công cụ thành công!", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo công cụ", "updateSuccess": "<PERSON><PERSON><PERSON> nhật công cụ thành công!", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật công cụ", "deleteSuccess": "<PERSON><PERSON><PERSON> công cụ thành công!", "deleteError": "Có lỗi xảy ra khi xóa công cụ", "activateSuccess": "<PERSON><PERSON><PERSON> ho<PERSON>t công cụ thành công!", "deactivateSuccess": "Vô hiệu hóa công cụ thành công!", "toggleError": "<PERSON><PERSON> lỗi xảy ra khi thay đổi trạng thái công cụ", "cloneSuccess": "<PERSON>o chép công cụ thành công!", "cloneError": "C<PERSON> lỗi xảy ra khi sao chép công cụ", "confirmDelete": "Bạn có chắc chắn muốn xóa công cụ này?", "confirmDeactivate": "Bạn có chắc chắn muốn vô hiệu hóa công cụ này?"}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> công cụ là bắt buộc", "nameMinLength": "<PERSON><PERSON><PERSON> công cụ phải có ít nhất 3 ký tự", "nameMaxLength": "<PERSON><PERSON><PERSON> công cụ không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự", "descriptionMaxLength": "<PERSON><PERSON> tả không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 500 ký tự", "versionRequired": "<PERSON><PERSON><PERSON> bả<PERSON> là b<PERSON> bu<PERSON>c", "versionFormat": "<PERSON><PERSON><PERSON> bản ph<PERSON>i có định dạng x.y.z", "contentRequired": "<PERSON>ội dung là bắt buộc", "schemaRequired": "<PERSON>hema là b<PERSON> bu<PERSON>c", "schemaInvalid": "<PERSON><PERSON><PERSON> h<PERSON>p l<PERSON>", "parametersInvalid": "<PERSON><PERSON> số không hợp lệ"}, "table": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "version": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "actions": "<PERSON><PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "hasUpdate": "<PERSON><PERSON> c<PERSON> nh<PERSON>t"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "active": "<PERSON><PERSON> ho<PERSON>t động", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "hasUpdate": "<PERSON><PERSON> c<PERSON> nh<PERSON>t", "noUpdate": "<PERSON><PERSON><PERSON><PERSON> có cập nhật", "search": "<PERSON><PERSON><PERSON> kiếm công cụ..."}, "versions": {"title": "<PERSON><PERSON><PERSON> bản công cụ", "current": "<PERSON><PERSON><PERSON> t<PERSON>i", "latest": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> phiên bản mới", "edit": "Chỉnh sửa phiên bản", "delete": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>n", "activate": "<PERSON><PERSON><PERSON> ho<PERSON><PERSON> bản", "changelog": "<PERSON><PERSON><PERSON><PERSON> ký thay đổi", "releaseNotes": "<PERSON><PERSON> chú phát hành"}, "integration": {"title": "<PERSON><PERSON><PERSON> h<PERSON> công c<PERSON>", "configure": "<PERSON><PERSON><PERSON> h<PERSON>nh", "test": "<PERSON><PERSON><PERSON> tra", "enable": "<PERSON><PERSON><PERSON>", "disable": "Tắt", "settings": "Cài đặt", "apiKey": "API Key", "webhook": "Webhook", "callback": "Callback URL", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "baseUrl": "URL gốc", "openapiSpec": "Đặc tả OpenAPI", "authentication": "<PERSON><PERSON><PERSON> th<PERSON>c", "authType": "<PERSON><PERSON><PERSON> x<PERSON>c thực", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên tích hợp", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả tích hợp", "baseUrlPlaceholder": "https://api.example.com", "openapiPlaceholder": "Dán đặc tả OpenAPI của bạn ở định dạng JSON", "viewIntegration": "<PERSON><PERSON> t<PERSON>", "editIntegration": "Chỉnh sửa tích hợp", "createIntegration": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>p", "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "refreshSpec": "<PERSON><PERSON><PERSON> mới đặc tả", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON>a", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa tích hợp này? Hành động này không thể hoàn tác.", "connectionTestResult": "<PERSON><PERSON><PERSON> quả kiểm tra kết nối", "apiKeyConfig": "<PERSON><PERSON>u hình API Key", "oauthConfig": "<PERSON><PERSON><PERSON> <PERSON>", "apiKeyLocation": "<PERSON><PERSON> trí", "paramName": "<PERSON>ên tham số", "paramNamePlaceholder": "ví dụ: X-API-Key", "schemeName": "<PERSON><PERSON><PERSON><PERSON>", "schemeNamePlaceholder": "ví dụ: <PERSON><PERSON><PERSON><PERSON><PERSON>", "apiKeyPlaceholder": "Nhập API key c<PERSON><PERSON> bạn", "token": "Token", "tokenPlaceholder": "<PERSON><PERSON><PERSON><PERSON> token của bạn", "tokenSource": "Nguồn token", "auth": {"none": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c thực", "apiKey": "API Key", "oauth": "OAuth"}, "table": {"name": "<PERSON><PERSON><PERSON>", "endpoint": "Endpoint", "method": "<PERSON><PERSON><PERSON><PERSON> thức", "authType": "<PERSON><PERSON><PERSON> x<PERSON>c thực", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>"}, "validation": {"invalidOpenApiFormat": "Định dạng OpenAPI không hợp lệ. <PERSON><PERSON><PERSON> bao gồm các thuộc t<PERSON>h openapi, info và paths.", "invalidJson": "Định dạng JSON không hợp lệ", "openapiRequired": "Đặc tả OpenAPI hợp lệ là bắt buộc", "invalidUrl": "Định dạng URL không hợp lệ", "apiKeyRequired": "API Key là bắt buộc", "paramNameRequired": "<PERSON>ên tham số là bắt buộc", "tokenRequired": "<PERSON><PERSON> là b<PERSON> bu<PERSON>c"}}, "management": {"title": "<PERSON><PERSON><PERSON><PERSON> lý công cụ", "overview": "<PERSON><PERSON><PERSON> quan", "statistics": "<PERSON><PERSON><PERSON><PERSON> kê", "usage": "Sử dụng", "performance": "<PERSON><PERSON><PERSON>", "logs": "<PERSON><PERSON><PERSON><PERSON> ký", "monitoring": "<PERSON><PERSON><PERSON><PERSON>"}}}