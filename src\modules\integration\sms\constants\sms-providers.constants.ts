import { SmsProviderTemplate, SmsProviderType } from '../types';

/**
 * SMS Provider Types Constants
 */
export const SMS_PROVIDER_TYPES = {
  TWILIO: 'twilio',
  AWS_SNS: 'aws-sns',
  VIETTEL: 'viettel',
  VNPT: 'vnpt',
  FPT: 'fpt',
  CUSTOM: 'custom',
} as const;

/**
 * SMS Provider Status Constants
 */
export const SMS_PROVIDER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  ERROR: 'error',
  TESTING: 'testing',
  PENDING: 'pending',
} as const;

/**
 * SMS Provider Templates
 */
export const SMS_PROVIDER_TEMPLATES: Record<SmsProviderType, SmsProviderTemplate> = {
  twilio: {
    type: 'twilio',
    name: 'twilio',
    displayName: 'Twilio',
    description: 'Dịch vụ SMS quốc tế hàng đầu với độ tin cậy cao',
    icon: 'message-circle',
    documentationUrl: 'https://www.twilio.com/docs/sms',
    requiredCredentials: ['accountSid', 'authToken'],
    optionalCredentials: ['fromNumber'],
    defaultSettings: {
      rateLimits: {
        perSecond: 1,
        perMinute: 60,
        perHour: 3600,
        perDay: 86400,
        perMonth: 2592000,
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 5,
        backoffMultiplier: 2,
      },
      enableDeliveryReports: true,
      enableOptOut: true,
      timezone: 'Asia/Ho_Chi_Minh',
    },
    configurationSteps: [
      'Đăng ký tài khoản Twilio',
      'Lấy Account SID và Auth Token từ Console',
      'Mua số điện thoại hoặc cấu hình Sender ID',
      'Cấu hình webhook (tùy chọn)',
    ],
  },
  'aws-sns': {
    type: 'aws-sns',
    name: 'aws-sns',
    displayName: 'AWS SNS',
    description: 'Dịch vụ SMS của Amazon Web Services',
    icon: 'cloud',
    documentationUrl: 'https://docs.aws.amazon.com/sns/latest/dg/sms_publish-to-phone.html',
    requiredCredentials: ['accessKeyId', 'secretAccessKey', 'region'],
    optionalCredentials: [],
    defaultSettings: {
      rateLimits: {
        perSecond: 1,
        perMinute: 20,
        perHour: 1200,
        perDay: 28800,
        perMonth: 864000,
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 5,
        backoffMultiplier: 2,
      },
      enableDeliveryReports: true,
      enableOptOut: true,
      timezone: 'Asia/Ho_Chi_Minh',
    },
    configurationSteps: [
      'Tạo IAM user với quyền SNS',
      'Lấy Access Key ID và Secret Access Key',
      'Chọn region phù hợp',
      'Cấu hình spending limits',
    ],
  },
  viettel: {
    type: 'viettel',
    name: 'viettel',
    displayName: 'Viettel SMS',
    description: 'Dịch vụ SMS của Viettel - Nhà mạng lớn nhất Việt Nam',
    icon: 'smartphone',
    documentationUrl: 'https://sms.viettel.vn/api-docs',
    requiredCredentials: ['apiKey', 'apiSecret', 'username'],
    optionalCredentials: ['fromName'],
    defaultSettings: {
      rateLimits: {
        perSecond: 2,
        perMinute: 100,
        perHour: 6000,
        perDay: 144000,
        perMonth: 4320000,
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 3,
        backoffMultiplier: 1.5,
      },
      enableDeliveryReports: true,
      enableOptOut: true,
      timezone: 'Asia/Ho_Chi_Minh',
    },
    configurationSteps: [
      'Đăng ký dịch vụ SMS Viettel',
      'Lấy API Key và Secret từ portal',
      'Cấu hình tên thương hiệu (Brand Name)',
      'Nạp tiền vào tài khoản',
    ],
  },
  vnpt: {
    type: 'vnpt',
    name: 'vnpt',
    displayName: 'VNPT SMS',
    description: 'Dịch vụ SMS của VNPT - Tập đoàn Bưu chính Viễn thông Việt Nam',
    icon: 'mail',
    documentationUrl: 'https://sms.vnpt.vn/api-docs',
    requiredCredentials: ['username', 'password', 'apiKey'],
    optionalCredentials: ['fromName'],
    defaultSettings: {
      rateLimits: {
        perSecond: 2,
        perMinute: 80,
        perHour: 4800,
        perDay: 115200,
        perMonth: 3456000,
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 3,
        backoffMultiplier: 1.5,
      },
      enableDeliveryReports: true,
      enableOptOut: true,
      timezone: 'Asia/Ho_Chi_Minh',
    },
    configurationSteps: [
      'Đăng ký dịch vụ SMS VNPT',
      'Lấy username, password và API Key',
      'Cấu hình tên thương hiệu',
      'Nạp tiền vào tài khoản',
    ],
  },
  fpt: {
    type: 'fpt',
    name: 'fpt',
    displayName: 'FPT SMS',
    description: 'Dịch vụ SMS của FPT Telecom',
    icon: 'send',
    documentationUrl: 'https://sms.fpt.vn/api-docs',
    requiredCredentials: ['apiKey', 'username', 'password'],
    optionalCredentials: ['fromName'],
    defaultSettings: {
      rateLimits: {
        perSecond: 1,
        perMinute: 60,
        perHour: 3600,
        perDay: 86400,
        perMonth: 2592000,
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 3,
        backoffMultiplier: 1.5,
      },
      enableDeliveryReports: true,
      enableOptOut: true,
      timezone: 'Asia/Ho_Chi_Minh',
    },
    configurationSteps: [
      'Đăng ký dịch vụ SMS FPT',
      'Lấy API Key, username và password',
      'Cấu hình tên thương hiệu',
      'Nạp tiền vào tài khoản',
    ],
  },
  custom: {
    type: 'custom',
    name: 'custom',
    displayName: 'Custom API',
    description: 'Tích hợp với API SMS tùy chỉnh',
    icon: 'settings',
    documentationUrl: '',
    requiredCredentials: ['endpoint', 'apiKey'],
    optionalCredentials: ['headers', 'username', 'password'],
    defaultSettings: {
      rateLimits: {
        perSecond: 1,
        perMinute: 60,
        perHour: 3600,
        perDay: 86400,
        perMonth: 2592000,
      },
      retryConfig: {
        maxRetries: 3,
        retryDelay: 5,
        backoffMultiplier: 2,
      },
      enableDeliveryReports: false,
      enableOptOut: false,
      timezone: 'Asia/Ho_Chi_Minh',
    },
    configurationSteps: [
      'Chuẩn bị endpoint API SMS',
      'Lấy API Key hoặc credentials',
      'Cấu hình headers nếu cần',
      'Test kết nối',
    ],
  },
};

/**
 * Default Rate Limits
 */
export const DEFAULT_RATE_LIMITS = {
  perSecond: 1,
  perMinute: 60,
  perHour: 3600,
  perDay: 86400,
  perMonth: 2592000,
};

/**
 * Default Retry Configuration
 */
export const DEFAULT_RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 5,
  backoffMultiplier: 2,
};

/**
 * SMS Integration API Endpoints
 */
export const SMS_INTEGRATION_ENDPOINTS = {
  PROVIDERS: '/user/integrations/sms/providers',
  PROVIDER_DETAIL: (id: string) => `/user/integrations/sms/providers/${id}`,
  TEST_PROVIDER: (id: string) => `/user/integrations/sms/providers/${id}/test`,
  PROVIDER_STATUS: (id: string) => `/user/integrations/sms/providers/${id}/status`,
  SEND_TEST_SMS: '/user/integrations/sms/test-send',
} as const;

/**
 * SMS Integration Query Keys for TanStack Query
 */
export const SMS_INTEGRATION_QUERY_KEYS = {
  ALL: ['sms-integration'] as const,
  PROVIDERS: () => [...SMS_INTEGRATION_QUERY_KEYS.ALL, 'providers'] as const,
  PROVIDER: (id: string) => [...SMS_INTEGRATION_QUERY_KEYS.PROVIDERS(), id] as const,
  PROVIDER_LIST: (params: Record<string, unknown>) =>
    [...SMS_INTEGRATION_QUERY_KEYS.PROVIDERS(), 'list', params] as const,
} as const;
